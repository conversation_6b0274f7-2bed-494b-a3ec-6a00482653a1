classDiagram
direction BT
class acl_role {
   varchar(100) codename
   varchar(150) title
   bigint id
}
class acl_role_permissions {
   bigint role_id
   integer permission_id
   bigint id
}
class acl_rule {
   integer object_id
   integer content_type_id
   bigint role_id
   bigint user_id
   bigint id
}
class acl_rule_groups {
   bigint rule_id
   integer group_id
   bigint id
}
class auth_group {
   varchar(150) name
   integer id
}
class auth_group_permissions {
   integer group_id
   integer permission_id
   bigint id
}
class auth_permission {
   varchar(255) name
   integer content_type_id
   varchar(100) codename
   integer id
}
class chat_ai_conversation {
   timestamp with time zone created
   timestamp with time zone modified
   bigint user_id
   uuid id
}
class chat_ai_conversation_layers {
   uuid conversation_id
   bigint layer_id
   bigint id
}
class chat_ai_message {
   timestamp with time zone created
   timestamp with time zone modified
   jsonb message
   uuid conversation_id
   bigint id
}
class django_admin_log {
   timestamp with time zone action_time
   text object_id
   varchar(200) object_repr
   smallint action_flag
   text change_message
   integer content_type_id
   bigint user_id
   integer id
}
class django_content_type {
   varchar(100) app_label
   varchar(100) model
   integer id
}
class django_migrations {
   varchar(255) app
   varchar(255) name
   timestamp with time zone applied
   bigint id
}
class django_session {
   text session_data
   timestamp with time zone expire_date
   varchar(40) session_key
}
class guardian_groupobjectpermission {
   varchar(255) object_pk
   integer content_type_id
   integer group_id
   integer permission_id
   integer id
}
class guardian_userobjectpermission {
   varchar(255) object_pk
   integer content_type_id
   integer permission_id
   bigint user_id
   integer id
}
class layers_layer {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(200) key
   varchar(200) title
   text description
   boolean read_only
   jsonb location_field_mapping
   jsonb json_schema
   jsonb web_ui_json_schema
   bigint dataset_id
   bigint workspace_id
   varchar(20) status
   geometry(geometry,4326) boundaries
   jsonb data
   timestamp with time zone records_last_modified
   bigint id
}
class layers_layer_slds {
   bigint layer_id
   bigint sld_id
   bigint id
}
class layers_record {
   timestamp with time zone created
   timestamp with time zone modified
   geometry(geometry,4326) geometry
   jsonb source_properties
   jsonb map_data
   jsonb data
   bigint layer_id
   geometry(geometry,4326) buffer_geometry
   integer weight
   bigint id
}
class layers_sld {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(200) title
   varchar(20) sld_type
   jsonb feature_style
   text xml_body
   bigint id
}
class organizations_organization {
   timestamp with time zone created
   integer external_id
   timestamp with time zone modified
   jsonb settings
   jsonb workspaces_data
   bigint id
}
class organizations_organization_roles {
   bigint organization_id
   bigint role_id
   bigint id
}
class push_notifications_apnsdevice {
   varchar(255) name
   boolean active
   timestamp with time zone date_created
   uuid device_id
   varchar(200) registration_id
   bigint user_id
   varchar(64) application_id
   integer id
}
class push_notifications_gcmdevice {
   varchar(255) name
   boolean active
   timestamp with time zone date_created
   bigint device_id
   text registration_id
   bigint user_id
   varchar(3) cloud_message_type
   varchar(64) application_id
   integer id
}
class push_notifications_webpushdevice {
   varchar(255) name
   boolean active
   timestamp with time zone date_created
   varchar(64) application_id
   text registration_id
   varchar(88) p256dh
   varchar(24) auth
   varchar(10) browser
   bigint user_id
   integer id
}
class push_notifications_wnsdevice {
   varchar(255) name
   boolean active
   timestamp with time zone date_created
   uuid device_id
   text registration_id
   bigint user_id
   varchar(64) application_id
   integer id
}
class reversion_revision {
   timestamp with time zone date_created
   text comment
   bigint user_id
   integer id
}
class reversion_version {
   varchar(191) object_id
   varchar(255) format
   text serialized_data
   text object_repr
   integer content_type_id
   integer revision_id
   varchar(191) db
   integer id
}
class spatial_ref_sys {
   varchar(256) auth_name
   integer auth_srid
   varchar(2048) srtext
   varchar(2048) proj4text
   integer srid
}
class uploads_upload {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(100) dataset
   integer object_id
   integer content_type_id
   bigint id
}
class users_user {
   varchar(128) password
   timestamp with time zone last_login
   boolean is_superuser
   integer external_key
   varchar(254) email
   varchar(128) phone
   varchar(32) first_name
   varchar(32) last_name
   timestamp with time zone created_at
   boolean is_staff
   varchar(200) avatar
   varchar(20) active_status
   bigint id
}
class users_user_groups {
   bigint user_id
   integer group_id
   bigint id
}
class users_user_user_permissions {
   bigint user_id
   integer permission_id
   bigint id
}
class workspaces_dataset {
   varchar(200) file
   timestamp with time zone created
   jsonb meta_data
   timestamp with time zone modified
   varchar(255) title
   bigint workspace_id
   bigint id
}
class workspaces_edareport {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(500) hash_code
   varchar(200) file
   varchar(20) source
   bigint dataset_id
   bigint id
}
class workspaces_workspace {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(255) name
   text description
   varchar(500) thumbnail
   timestamp with time zone last_visited
   bigint owner_id
   bigint organization_id
   jsonb layers_sorted_ids
   jsonb layers_data
   varchar(20) workspace_type
   bigint id
}
class workspaces_workspacerequest {
   timestamp with time zone created
   timestamp with time zone modified
   varchar(20) status
   jsonb layer_data
   bigint created_by_id
   bigint dataset_id
   bigint layer_id
   bigint organization_id
   varchar(20) request_type
   bigint id
}

acl_role_permissions  -->  acl_role : role_id:id
acl_role_permissions  -->  auth_permission : permission_id:id
acl_rule  -->  acl_role : role_id:id
acl_rule  -->  django_content_type : content_type_id:id
acl_rule  -->  users_user : user_id:id
acl_rule_groups  -->  acl_rule : rule_id:id
acl_rule_groups  -->  auth_group : group_id:id
auth_group_permissions  -->  auth_group : group_id:id
auth_group_permissions  -->  auth_permission : permission_id:id
auth_permission  -->  django_content_type : content_type_id:id
chat_ai_conversation  -->  users_user : user_id:id
chat_ai_conversation_layers  -->  chat_ai_conversation : conversation_id:id
chat_ai_conversation_layers  -->  layers_layer : layer_id:id
chat_ai_message  -->  chat_ai_conversation : conversation_id:id
django_admin_log  -->  django_content_type : content_type_id:id
django_admin_log  -->  users_user : user_id:id
guardian_groupobjectpermission  -->  auth_group : group_id:id
guardian_groupobjectpermission  -->  auth_permission : permission_id:id
guardian_groupobjectpermission  -->  django_content_type : content_type_id:id
guardian_userobjectpermission  -->  auth_permission : permission_id:id
guardian_userobjectpermission  -->  django_content_type : content_type_id:id
guardian_userobjectpermission  -->  users_user : user_id:id
layers_layer  -->  workspaces_dataset : dataset_id:id
layers_layer  -->  workspaces_workspace : workspace_id:id
layers_layer_slds  -->  layers_layer : layer_id:id
layers_layer_slds  -->  layers_sld : sld_id:id
layers_record  -->  layers_layer : layer_id:id
organizations_organization_roles  -->  acl_role : role_id:id
organizations_organization_roles  -->  organizations_organization : organization_id:id
push_notifications_apnsdevice  -->  users_user : user_id:id
push_notifications_gcmdevice  -->  users_user : user_id:id
push_notifications_webpushdevice  -->  users_user : user_id:id
push_notifications_wnsdevice  -->  users_user : user_id:id
reversion_revision  -->  users_user : user_id:id
reversion_version  -->  django_content_type : content_type_id:id
reversion_version  -->  reversion_revision : revision_id:id
uploads_upload  -->  django_content_type : content_type_id:id
users_user_groups  -->  auth_group : group_id:id
users_user_groups  -->  users_user : user_id:id
users_user_user_permissions  -->  auth_permission : permission_id:id
users_user_user_permissions  -->  users_user : user_id:id
workspaces_dataset  -->  workspaces_workspace : workspace_id:id
workspaces_edareport  -->  workspaces_dataset : dataset_id:id
workspaces_workspace  -->  organizations_organization : organization_id:id
workspaces_workspace  -->  users_user : owner_id:id
workspaces_workspacerequest  -->  layers_layer : layer_id:id
workspaces_workspacerequest  -->  organizations_organization : organization_id:id
workspaces_workspacerequest  -->  users_user : created_by_id:id
workspaces_workspacerequest  -->  workspaces_dataset : dataset_id:id
