
import logging

from databases import Database

from connectors import ChatGPTConnector, DeepseekConnector

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def llm_execution(messages: str) -> str:
        # ask ChatGpt First
    answer = ChatGPTConnector().complete(messages=messages)
    return answer


async def execute_sql(sql_query: str) -> list[dict]:
    """Execute a SQL query and return the result as rows."""
    rows = []
    try:
        database = Database("postgresql://abdallah:199712@localhost:5432/geocore")
        if not database.is_connected:
            await database.connect()

        rows = await database.fetch_all(query=sql_query)
    except Exception as e:
        logger.debug(
            f"execute_sql: {e}"
        )
    return [dict(row) for row in rows]


