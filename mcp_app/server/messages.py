# from .

def human_language_to_sql_message(user_input:str, schema:str, prompt: str):
    system_message =  f"""
        You are an expert Postgres SQL generator.
        Given (1) a database schema and a natural-language question:
        - Use ONLY the tables/columns that exist in the provided schema.
        - Output ONE valid SQL statement that answers the question.
        - Target DIALECT: PostgreSQL 14+.
        - NO commentary, NO explanation.
        - If the request cannot be answered with the given schema, output exactly: -- IMPOSSIBLE
        - Wrap the SQL in triple backticks with a `sql` tag.
        
        SCHEMA:
        {schema}
        
        OPTIONAL GUIDANCE (may be empty):
        {prompt}
        
        Return format:
        ```sql
        ...your query...
    """
    user_message = user_input
    return [{"role": "system", "content": system_message}, {"role": "user", "content": user_message}]

def llm_response_message(user_input: str, sql: str, prompt: str = ""):
    system_message = f"""
        You are a helpful data analyst.
        Inputs:
        - SQL (Postgres): 
        {sql}
        Guidance:
        {prompt}
        
        Your task:
        - Write a one-sentence answer to the user’s question.
        - Add 1–3 bullet insights (totals, top items, trends, date ranges).
        - Mention caveats (nulls, filters, missing data) if relevant.
        - Keep the response under 120 words.
        - Do NOT include SQL in the answer.
    """

    return [
        {"role": "system", "content": system_message.strip()},
        {"role": "user", "content": user_input},
    ]


