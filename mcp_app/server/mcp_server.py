# server.py
import logging
import sys

from dotenv import load_dotenv

load_dotenv()

from mcp.server.fastmcp import FastMCP
from messages import human_language_to_sql_message, llm_response_message
from prompts import geocore_sql_prompts, geocore_summarize_sql_result
from resources import geocore_db_schema
from tools import llm_execution, execute_sql
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)

mcp = FastMCP(name="Geotech", instructions=None,
    host="localhost",
    port=6274)


# Tools

@mcp.tool("talk_to_llm")
def talk_to_llm(user_question: str) -> str:
    """Tool to ask Any LLM about the database using schema and prompt."""
    natural_language_to_sql = llm_execution(
        messages=human_language_to_sql_message(user_input=user_question, schema=get_db_schema(), prompt=sql_prompt())
    )
    logging.debug(
        f"natural_language_to_sql: {natural_language_to_sql}"
    )
    query_result = execute_sql_query(sql_query=natural_language_to_sql)
    logging.debug(
        f"query_result: {query_result}"
    )
    response = llm_execution(
        messages=llm_response_message(
            user_input=user_question, prompt=summarize_sql_result_prompt(), sql=query_result
        ),
     )
    logging.debug(
        f"response: {response}"
    )
    return response


@mcp.tool("execute_sql")
async def execute_sql_query(sql_query: str) -> list[dict]:
    return await  execute_sql(sql_query)


# Resources

@mcp.resource(
    uri="schema://db-json",
    name="GeoCore Database Schema (JSON)",
    description="Current DB schema snapshot in Markdown for GeoCore.",
    mime_type="application/json",
)
def get_db_schema() -> dict:
    return geocore_db_schema()

# Prompts

@mcp.prompt("Answer database questions based on schema only.")
def sql_prompt() -> str:
    """Guide ChatGPT to only generate SQL or explanations."""
    return geocore_sql_prompts()


@mcp.prompt("Summarize the SQL result into a natural language answer.")
def summarize_sql_result_prompt() -> str:
    """Guide ChatGPT To Summarize the SQL result into a natural language answer."""
    return geocore_summarize_sql_result()




if __name__ == "__main__":
    mcp.run(transport="stdio")
