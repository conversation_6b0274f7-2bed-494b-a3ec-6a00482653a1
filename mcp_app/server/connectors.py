from openai import OpenAI

class ChatGPTConnector:
    def __init__(self):
        self.model = "gpt-4"
        self.openai_api_key = "******************************************************************************************************************************************************"
        self.client = OpenAI(api_key=self.openai_api_key)

    def complete(self, messages, **kwargs):
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=0.2,
        )
        return response.choices[0].message.content


class DeepseekConnector:
    def __init__(self):
        self.model = "deepseek-ai/deepseek-r1"
        self.base_url = "https://integrate.api.nvidia.com/v1"
        self.api_key = "**********************************************************************"
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)


    def complete(self, messages, **kwargs):
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
          #   temperature=0.6,
          #   top_p=0.7,
          # max_tokens=4096,
          stream=True
        )
        full_response = ""
        for chunk in response:
            delta = chunk.choices[0].delta
            if delta and delta.content:
                content = delta.content
                print(content, end="", flush=True)  # Stream to console
                full_response += content  # Accumulate full response

        return full_response
