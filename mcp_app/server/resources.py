def geocore_db_schema() -> dict:
    """Return the Markdown schema file as text."""
    return {
  "tables": {
    "spatial_ref_sys": {
      "columns": {
        "srid": "integer",
        "auth_name": "character varying",
        "auth_srid": "integer",
        "srtext": "character varying",
        "proj4text": "character varying"
      }
    },
    "workspaces_workspacerequest": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "status": "character varying",
        "layer_data": "jsonb",
        "created_by_id": "bigint",
        "dataset_id": "bigint",
        "layer_id": "bigint",
        "organization_id": "bigint",
        "request_type": "character varying"
      }
    },
    "guardian_groupobjectpermission": {
      "columns": {
        "id": "integer",
        "object_pk": "character varying",
        "content_type_id": "integer",
        "group_id": "integer",
        "permission_id": "integer"
      }
    },
    "guardian_userobjectpermission": {
      "columns": {
        "id": "integer",
        "object_pk": "character varying",
        "content_type_id": "integer",
        "permission_id": "integer",
        "user_id": "bigint"
      }
    },
    "workspaces_workspace": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "name": "character varying",
        "description": "text",
        "thumbnail": "character varying",
        "last_visited": "timestamp with time zone",
        "owner_id": "bigint",
        "organization_id": "bigint",
        "layers_sorted_ids": "jsonb",
        "layers_data": "jsonb",
        "workspace_type": "character varying"
      }
    },
    "workspaces_dataset": {
      "columns": {
        "id": "bigint",
        "file": "character varying",
        "created": "timestamp with time zone",
        "meta_data": "jsonb",
        "modified": "timestamp with time zone",
        "title": "character varying",
        "workspace_id": "bigint"
      }
    },
    "django_migrations": {
      "columns": {
        "id": "bigint",
        "app": "character varying",
        "name": "character varying",
        "applied": "timestamp with time zone"
      }
    },
    "django_content_type": {
      "columns": {
        "id": "integer",
        "app_label": "character varying",
        "model": "character varying"
      }
    },
    "auth_permission": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "content_type_id": "integer",
        "codename": "character varying"
      }
    },
    "auth_group": {
      "columns": {
        "id": "integer",
        "name": "character varying"
      }
    },
    "auth_group_permissions": {
      "columns": {
        "id": "bigint",
        "group_id": "integer",
        "permission_id": "integer"
      }
    },
    "users_user": {
      "columns": {
        "id": "bigint",
        "password": "character varying",
        "last_login": "timestamp with time zone",
        "is_superuser": "boolean",
        "external_key": "integer",
        "email": "character varying",
        "phone": "character varying",
        "first_name": "character varying",
        "last_name": "character varying",
        "created_at": "timestamp with time zone",
        "is_staff": "boolean",
        "avatar": "character varying",
        "active_status": "character varying"
      }
    },
    "users_user_groups": {
      "columns": {
        "id": "bigint",
        "user_id": "bigint",
        "group_id": "integer"
      }
    },
    "users_user_user_permissions": {
      "columns": {
        "id": "bigint",
        "user_id": "bigint",
        "permission_id": "integer"
      }
    },
    "acl_role": {
      "columns": {
        "id": "bigint",
        "codename": "character varying",
        "title": "character varying"
      }
    },
    "acl_role_permissions": {
      "columns": {
        "id": "bigint",
        "role_id": "bigint",
        "permission_id": "integer"
      }
    },
    "acl_rule": {
      "columns": {
        "id": "bigint",
        "object_id": "integer",
        "content_type_id": "integer",
        "role_id": "bigint",
        "user_id": "bigint"
      }
    },
    "acl_rule_groups": {
      "columns": {
        "id": "bigint",
        "rule_id": "bigint",
        "group_id": "integer"
      }
    },
    "django_admin_log": {
      "columns": {
        "id": "integer",
        "action_time": "timestamp with time zone",
        "object_id": "text",
        "object_repr": "character varying",
        "action_flag": "smallint",
        "change_message": "text",
        "content_type_id": "integer",
        "user_id": "bigint"
      }
    },
    "organizations_organization": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "external_id": "integer",
        "modified": "timestamp with time zone",
        "settings": "jsonb",
        "workspaces_data": "jsonb"
      }
    },
    "layers_record": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "geometry": "USER-DEFINED",
        "source_properties": "jsonb",
        "map_data": "jsonb",
        "data": "jsonb",
        "layer_id": "bigint",
        "buffer_geometry": "USER-DEFINED",
        "weight": "integer"
      }
    },
    "layers_sld": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "title": "character varying",
        "sld_type": "character varying",
        "feature_style": "jsonb",
        "xml_body": "text"
      }
    },
    "chat_ai_conversation": {
      "columns": {
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "id": "uuid",
        "user_id": "bigint"
      }
    },
    "chat_ai_message": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "message": "jsonb",
        "conversation_id": "uuid"
      }
    },
    "chat_ai_conversation_layers": {
      "columns": {
        "id": "bigint",
        "conversation_id": "uuid",
        "layer_id": "bigint"
      }
    },
    "layers_layer": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "key": "character varying",
        "title": "character varying",
        "description": "text",
        "read_only": "boolean",
        "location_field_mapping": "jsonb",
        "json_schema": "jsonb",
        "web_ui_json_schema": "jsonb",
        "dataset_id": "bigint",
        "workspace_id": "bigint",
        "status": "character varying",
        "boundaries": "USER-DEFINED",
        "data": "jsonb",
        "records_last_modified": "timestamp with time zone"
      }
    },
    "layers_layer_slds": {
      "columns": {
        "id": "bigint",
        "layer_id": "bigint",
        "sld_id": "bigint"
      }
    },
    "organizations_organization_roles": {
      "columns": {
        "id": "bigint",
        "organization_id": "bigint",
        "role_id": "bigint"
      }
    },
    "push_notifications_apnsdevice": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "uuid",
        "registration_id": "character varying",
        "user_id": "bigint",
        "application_id": "character varying"
      }
    },
    "push_notifications_gcmdevice": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "bigint",
        "registration_id": "text",
        "user_id": "bigint",
        "cloud_message_type": "character varying",
        "application_id": "character varying"
      }
    },
    "push_notifications_wnsdevice": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "device_id": "uuid",
        "registration_id": "text",
        "user_id": "bigint",
        "application_id": "character varying"
      }
    },
    "push_notifications_webpushdevice": {
      "columns": {
        "id": "integer",
        "name": "character varying",
        "active": "boolean",
        "date_created": "timestamp with time zone",
        "application_id": "character varying",
        "registration_id": "text",
        "p256dh": "character varying",
        "auth": "character varying",
        "browser": "character varying",
        "user_id": "bigint"
      }
    },
    "reversion_revision": {
      "columns": {
        "id": "integer",
        "date_created": "timestamp with time zone",
        "comment": "text",
        "user_id": "bigint"
      }
    },
    "reversion_version": {
      "columns": {
        "id": "integer",
        "object_id": "character varying",
        "format": "character varying",
        "serialized_data": "text",
        "object_repr": "text",
        "content_type_id": "integer",
        "revision_id": "integer",
        "db": "character varying"
      }
    },
    "django_session": {
      "columns": {
        "session_key": "character varying",
        "session_data": "text",
        "expire_date": "timestamp with time zone"
      }
    },
    "uploads_upload": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "dataset": "character varying",
        "object_id": "integer",
        "content_type_id": "integer"
      }
    },
    "workspaces_edareport": {
      "columns": {
        "id": "bigint",
        "created": "timestamp with time zone",
        "modified": "timestamp with time zone",
        "hash_code": "character varying",
        "file": "character varying",
        "source": "character varying",
        "dataset_id": "bigint"
      }
    }
  }
}