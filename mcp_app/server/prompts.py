

def geocore_sql_prompts():
    return """
        - Only generate SELECT statements (read-only).
        - Use explicit JOIN syntax.
        - Never return more than 50 rows unless asked.
        - If the question is ambiguous, choose the simplest interpretation.
        - Only generate read-only queries. Never use INSERT, UPDATE, DELETE, or DROP.
        - Always include a LIMIT clause for queries that could return many rows.
        - For text filtering, use ILIKE instead of LIKE to make searches case-insensitive.
        - When the user asks “how many,” generate a COUNT(*).
        - When asked for “average,” use AVG(column).
        - When asked for “most,” use ORDER BY … DESC LIMIT 1.
        - When asked for “top N,” generate ORDER BY with LIMIT N.
        - When asked for recent data, default to the last 30 days using CURRENT_DATE - INTERVAL '30 days'.
        - For “today,” filter with CURRENT_DATE.
        - Always order by timestamp columns descending unless otherwise specified.
        - When asked for recent data, default to the last 30 days using CURRENT_DATE - INTERVAL '30 days'.
        - For “today,” filter with CURRENT_DATE.
        - Always order by timestamp columns descending unless otherwise specified.
        """


def geocore_summarize_sql_result():
    return """
        - Answer in plain English, as if explaining to a non-technical stakeholder.
        - Start with a clear one-sentence answer to the user’s question.
        - Then add 1–3 concise bullet insights (totals, top items, trends, or comparisons).
        - Highlight important caveats (missing data, filters, null values) if they affect the result.
        - Keep the whole response under 120 words.
        - Do not include SQL or technical jargon in the answer.
    """